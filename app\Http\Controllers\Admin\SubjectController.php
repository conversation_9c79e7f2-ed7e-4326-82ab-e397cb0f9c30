<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Subject;
use App\Models\ClassModel;
use App\Models\ActivityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SubjectController extends Controller
{
    /**
     * Display a listing of subjects
     */
    public function index(Request $request)
    {
        $query = Subject::with(['classes', 'teachers']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('subject_code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('category', 'like', "%{$search}%");
            });
        }

        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        if ($request->filled('grade_level')) {
            $query->whereJsonContains('grade_levels', $request->grade_level);
        }

        $subjects = $query->orderBy('name')->paginate(20);

        // Get filter options
        $categories = Subject::distinct()->pluck('category')->filter()->sort();
        $gradeLevels = ClassModel::active()->ordered()->pluck('name');

        // Statistics
        $stats = [
            'total_subjects' => Subject::count(),
            'active_subjects' => Subject::active()->count(),
            'core_subjects' => Subject::where('category', 'Core')->count(),
            'elective_subjects' => Subject::where('category', 'Elective')->count(),
        ];

        return view('admin.subjects.index', compact('subjects', 'categories', 'gradeLevels', 'stats'));
    }

    /**
     * Show the form for creating a new subject
     */
    public function create()
    {
        $classes = ClassModel::active()->ordered()->get();
        $existingSubjects = Subject::active()->orderBy('name')->get();
        
        return view('admin.subjects.create', compact('classes', 'existingSubjects'));
    }

    /**
     * Store a newly created subject
     */
    public function store(Request $request)
    {
        $request->validate([
            'subject_code' => 'required|string|max:20|unique:subjects',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'category' => 'required|in:Core,Elective,Extra-curricular',
            'credits' => 'required|integer|min:1|max:10',
            'grade_levels' => 'nullable|array',
            'grade_levels.*' => 'string',
            'prerequisites' => 'nullable|array',
            'prerequisites.*' => 'exists:subjects,id',
            'is_active' => 'boolean',
        ]);

        try {
            DB::beginTransaction();

            $subject = Subject::create([
                'subject_code' => $request->subject_code,
                'name' => $request->name,
                'description' => $request->description,
                'category' => $request->category,
                'credits' => $request->credits,
                'grade_levels' => $request->grade_levels,
                'prerequisites' => $request->prerequisites,
                'is_active' => $request->boolean('is_active', true),
            ]);

            // Log activity
            ActivityLog::log(
                'created_subject',
                "Created subject: {$subject->name} ({$subject->subject_code})",
                'App\Models\Subject',
                $subject->id
            );

            DB::commit();

            return redirect()->route('admin.academic.subjects.index')
                           ->with('success', 'Subject created successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                        ->with('error', 'Error creating subject: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified subject
     */
    public function show(Subject $subject)
    {
        $subject->load(['classes', 'teachers.user']);

        // Load prerequisite and dependent subjects manually since they're not proper relationships
        $prerequisiteSubjects = $subject->prerequisiteSubjects();
        $dependentSubjects = $subject->dependentSubjects();

        return view('admin.subjects.show', compact('subject', 'prerequisiteSubjects', 'dependentSubjects'));
    }

    /**
     * Show the form for editing the specified subject
     */
    public function edit(Subject $subject)
    {
        $classes = ClassModel::active()->ordered()->get();
        $existingSubjects = Subject::active()->where('id', '!=', $subject->id)->orderBy('name')->get();
        
        return view('admin.subjects.edit', compact('subject', 'classes', 'existingSubjects'));
    }

    /**
     * Update the specified subject
     */
    public function update(Request $request, Subject $subject)
    {
        $request->validate([
            'subject_code' => 'required|string|max:20|unique:subjects,subject_code,' . $subject->id,
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'category' => 'required|in:Core,Elective,Extra-curricular',
            'credits' => 'required|integer|min:1|max:10',
            'grade_levels' => 'nullable|array',
            'grade_levels.*' => 'string',
            'prerequisites' => 'nullable|array',
            'prerequisites.*' => 'exists:subjects,id',
            'is_active' => 'boolean',
        ]);

        try {
            DB::beginTransaction();

            $oldData = $subject->toArray();

            $subject->update([
                'subject_code' => $request->subject_code,
                'name' => $request->name,
                'description' => $request->description,
                'category' => $request->category,
                'credits' => $request->credits,
                'grade_levels' => $request->grade_levels,
                'prerequisites' => $request->prerequisites,
                'is_active' => $request->boolean('is_active', true),
            ]);

            // Log activity
            ActivityLog::log(
                'updated_subject',
                "Updated subject: {$subject->name} ({$subject->subject_code})",
                'App\Models\Subject',
                $subject->id,
                ['old_data' => $oldData, 'new_data' => $subject->fresh()->toArray()]
            );

            DB::commit();

            return redirect()->route('admin.academic.subjects.index')
                           ->with('success', 'Subject updated successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                        ->with('error', 'Error updating subject: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified subject
     */
    public function destroy(Subject $subject)
    {
        try {
            DB::beginTransaction();

            // Check if subject is assigned to any classes or teachers
            if ($subject->classes()->count() > 0 || $subject->teachers()->count() > 0) {
                return back()->with('error', 'Cannot delete subject that is assigned to classes or teachers.');
            }

            // Check if subject is a prerequisite for other subjects
            if ($subject->dependentSubjects()->count() > 0) {
                return back()->with('error', 'Cannot delete subject that is a prerequisite for other subjects.');
            }

            $subjectName = $subject->name;
            $subjectCode = $subject->subject_code;

            $subject->delete();

            // Log activity
            ActivityLog::log(
                'deleted_subject',
                "Deleted subject: {$subjectName} ({$subjectCode})",
                'App\Models\Subject',
                null
            );

            DB::commit();

            return redirect()->route('admin.academic.subjects.index')
                           ->with('success', 'Subject deleted successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Error deleting subject: ' . $e->getMessage());
        }
    }

    /**
     * Toggle subject status
     */
    public function toggleStatus(Subject $subject)
    {
        try {
            $subject->update(['is_active' => !$subject->is_active]);

            $status = $subject->is_active ? 'activated' : 'deactivated';
            
            // Log activity
            ActivityLog::log(
                $status . '_subject',
                "Subject {$status}: {$subject->name} ({$subject->subject_code})",
                'App\Models\Subject',
                $subject->id
            );

            return back()->with('success', "Subject {$status} successfully.");

        } catch (\Exception $e) {
            return back()->with('error', 'Error updating subject status: ' . $e->getMessage());
        }
    }
}
